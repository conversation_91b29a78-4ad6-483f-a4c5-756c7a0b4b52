<?php

namespace App\Livewire\Admin;

use App\Models\Dentist;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

#[Layout('components.layouts.app')]
class PushNotifications extends Component
{
    use WithPagination;

    // Form fields
    public $title = '';
    public $message = '';
    
    // Dentist selection
    public $selectedDentists = [];
    public $selectAll = false;
    
    // Search functionality
    public $search = '';
    public $statusFilter = '';
    
    // UI state
    public $showPreview = false;
    
    protected $rules = [
        'title' => 'required|string|max:255',
        'message' => 'required|string|max:1000',
        'selectedDentists' => 'required|array|min:1',
        'selectedDentists.*' => 'exists:dentists,id',
    ];

    protected $messages = [
        'title.required' => 'Notification title is required.',
        'title.max' => 'Title cannot exceed 255 characters.',
        'message.required' => 'Notification message is required.',
        'message.max' => 'Message cannot exceed 1000 characters.',
        'selectedDentists.required' => 'Please select at least one dentist.',
        'selectedDentists.min' => 'Please select at least one dentist.',
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->updateSelectAllState();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
        $this->updateSelectAllState();
    }

    public function updatedSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedDentists = $this->getFilteredDentists()->pluck('id')->toArray();
        } else {
            $this->selectedDentists = [];
        }
    }

    public function updatedSelectedDentists()
    {
        $this->updateSelectAllState();
    }

    private function updateSelectAllState()
    {
        $filteredDentistIds = $this->getFilteredDentists()->pluck('id')->toArray();
        $selectedCount = count(array_intersect($this->selectedDentists, $filteredDentistIds));
        $totalCount = count($filteredDentistIds);
        
        $this->selectAll = $totalCount > 0 && $selectedCount === $totalCount;
    }

    private function getFilteredDentists()
    {
        return Dentist::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%')
                      ->orWhere('phone', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->statusFilter !== '', function ($query) {
                $query->where('is_active', $this->statusFilter === 'active');
            })
            ->orderBy('name')
            ->get();
    }

    public function toggleDentist($dentistId)
    {
        if (in_array($dentistId, $this->selectedDentists)) {
            $this->selectedDentists = array_values(array_diff($this->selectedDentists, [$dentistId]));
        } else {
            $this->selectedDentists[] = $dentistId;
        }
        
        $this->updateSelectAllState();
    }

    public function clearSelection()
    {
        $this->selectedDentists = [];
        $this->selectAll = false;
    }

    public function selectAllVisible()
    {
        $this->selectedDentists = array_unique(array_merge(
            $this->selectedDentists,
            $this->getFilteredDentists()->pluck('id')->toArray()
        ));
        $this->updateSelectAllState();
    }

    public function togglePreview()
    {
        $this->showPreview = !$this->showPreview;
    }

    public function sendNotification()
    {
        $this->validate();

        // TODO: Implement Firebase push notification logic here
        // For now, just simulate API call with a small delay
        sleep(1);

        $selectedCount = count($this->selectedDentists);

        // Reset form
        $this->reset(['title', 'message', 'selectedDentists', 'selectAll', 'showPreview']);

        // Show success message with count
        $this->dispatch('notify',
            variant: 'success',
            title: 'Success',
            message: "Push notification sent to {$selectedCount} dentist" . ($selectedCount > 1 ? 's' : '') . " successfully!"
        );
    }

    public function getTitleCharacterCount()
    {
        return strlen($this->title);
    }

    public function getMessageCharacterCount()
    {
        return strlen($this->message);
    }

    public function render()
    {
        $dentists = Dentist::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%')
                      ->orWhere('phone', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->statusFilter !== '', function ($query) {
                $query->where('is_active', $this->statusFilter === 'active');
            })
            ->orderBy('name')
            ->paginate(10);

        $selectedDentistDetails = Dentist::whereIn('id', $this->selectedDentists)->get();

        return view('livewire.admin.push-notifications', [
            'dentists' => $dentists,
            'selectedDentistDetails' => $selectedDentistDetails,
            'totalDentists' => Dentist::count(),
            'activeDentists' => Dentist::where('is_active', true)->count(),
            'selectedCount' => count($this->selectedDentists),
        ]);
    }
}
