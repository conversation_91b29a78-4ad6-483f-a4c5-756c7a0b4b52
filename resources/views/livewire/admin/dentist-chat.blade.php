<div class="h-screen flex bg-slate-50 dark:bg-slate-900 relative">
    <!-- Mobile Backdrop -->
    @if ($showSidebar)
        <div class="fixed inset-0 bg-black bg-opacity-50 z-5 lg:hidden" wire:click="toggleSidebar"></div>
    @endif

    <!-- Left Sidebar - Chat List -->
    <div
        class="w-80 lg:w-80 {{ $showSidebar ? 'block' : 'hidden lg:block' }} bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 flex flex-col absolute lg:relative z-10 lg:z-auto h-full lg:h-auto">
        <!-- Header -->
        <div class="p-4 border-b border-slate-200 dark:border-slate-700">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-slate-900 dark:text-white">Dentist Chats</h2>
                <div class="flex items-center space-x-2">
                    <!-- Close Sidebar Button (Mobile Only) -->
                    <button wire:click="toggleSidebar"
                        class="lg:hidden p-1 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                    <flux:badge variant="primary" size="sm">
                        {{ $chats->count() }} Chats
                    </flux:badge>
                </div>
            </div>

            <!-- New Chat Button -->
            <div class="mb-4">
                <flux:button wire:click="openNewChatModal" variant="primary" size="sm" icon="plus"
                    class="w-full">
                    New Chat
                </flux:button>
            </div>

            <!-- Search -->
            <div class="relative">
                <flux:input wire:model.live.debounce.300ms="searchDentists" placeholder="Search dentists..."
                    icon="magnifying-glass" />
            </div>
        </div>

        <!-- Chat List -->
        <div class="flex-1 overflow-y-auto">
            <!-- Existing Chats -->
            @forelse($chats as $chat)
                <div wire:click="selectChat({{ $chat->id }})"
                    class="p-4 border-b border-slate-100 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer transition-colors {{ $selectedChatId == $chat->id ? 'bg-indigo-50 dark:bg-indigo-900/20 border-l-4 border-l-indigo-500' : '' }}">
                    <div class="flex items-center space-x-3">
                        <!-- Avatar -->
                        <div
                            class="h-10 w-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                            {{ $chat->dentist->initials() }}
                        </div>

                        <!-- Chat Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <h3 class="text-sm font-medium text-slate-900 dark:text-white truncate">
                                    {{ $chat->dentist->name }}
                                </h3>
                                @if ($chat->latestMessage->first())
                                    <span class="text-xs text-slate-500 dark:text-slate-400">
                                        {{ $chat->latestMessage->first()->created_at->format('M j') }}
                                    </span>
                                @endif
                            </div>

                            <div class="flex items-center justify-between mt-1">
                                <p class="text-sm text-slate-600 dark:text-slate-300 truncate">
                                    @if ($chat->latestMessage->first())
                                        @if ($chat->latestMessage->first()->message)
                                            {{ Str::limit($chat->latestMessage->first()->message, 30) }}
                                        @elseif($chat->latestMessage->first()->file)
                                            <span class="flex items-center">
                                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13">
                                                    </path>
                                                </svg>
                                                File attachment
                                            </span>
                                        @endif
                                    @else
                                        <span class="text-slate-400 italic">No messages yet</span>
                                    @endif
                                </p>

                                @if ($chat->unreadMessagesCount() > 0)
                                    <flux:badge variant="danger" size="sm">
                                        {{ $chat->unreadMessagesCount() }}
                                    </flux:badge>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="p-8 text-center">
                    <svg class="mx-auto h-12 w-12 text-slate-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                        fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M4.804 21.644A6.707 6.707 0 0 0 6 21.75a6.721 6.721 0 0 0 3.583-1.029c.774.182 1.584.279 2.417.279 5.322 0 9.75-3.97 9.75-9 0-5.03-4.428-9-9.75-9s-9.75 3.97-9.75 9c0 2.409 1.025 4.587 2.674 *************.277.428.254.543a3.73 3.73 0 0 1-.814 1.686.75.75 0 0 0 .44 1.223ZM8.25 10.875a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25ZM10.875 12a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Zm4.875-1.125a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25Z"
                            clip-rule="evenodd" />
                    </svg>

                    <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No chats found</h3>
                    <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">Click "New Chat" to start a conversation.
                    </p>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Right Side - Chat Area -->
    <div class="flex-1 flex flex-col {{ $showSidebar ? 'hidden lg:flex' : 'flex' }}">
        @if ($selectedDentist)
            <!-- Chat Header -->
            <div class="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-4">
                <div class="flex items-center space-x-3">
                    <!-- Toggle Sidebar Button (Mobile Only) -->
                    <button wire:click="toggleSidebar"
                        class="lg:hidden p-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>

                    <div
                        class="h-10 w-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-medium">
                        {{ $selectedDentist->initials() }}
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-slate-900 dark:text-white">
                            {{ $selectedDentist->name }}
                        </h3>
                        <p class="text-sm text-slate-500 dark:text-slate-400">
                            {{ $selectedDentist->email }}
                        </p>
                    </div>
                    <div class="ml-auto">
                        <flux:badge variant="{{ $selectedDentist->is_active ? 'success' : 'danger' }}" size="sm">
                            {{ $selectedDentist->is_active ? 'Active' : 'Inactive' }}
                        </flux:badge>
                    </div>
                </div>
            </div>

            <!-- Messages Area -->
            <div class="flex-1 overflow-y-auto p-4 space-y-4" id="messages-container">
                @forelse($messages as $message)
                    <div class="flex {{ $message['sender'] === 'admin' ? 'justify-end' : 'justify-start' }}">
                        <div class="max-w-xs lg:max-w-md">
                            <!-- Message Bubble -->
                            <div
                                class="px-4 py-2 rounded-lg {{ $message['sender'] === 'admin' ? 'bg-indigo-500 text-white' : 'bg-white dark:bg-slate-700 text-slate-900 dark:text-white border border-slate-200 dark:border-slate-600' }}">
                                @if ($message['message'])
                                    <p class="text-sm">{{ $message['message'] }}</p>
                                @endif

                                @if ($message['file'])
                                    <div class="mt-2">
                                        @if ($message['file_type'] === 'image')
                                            <img src="{{ asset('storage/' . $message['file']) }}" alt="Image"
                                                class="max-w-full h-auto rounded-lg">
                                        @elseif($message['file_type'] === 'video')
                                            <video controls class="max-w-full h-auto rounded-lg">
                                                <source src="{{ asset('storage/' . $message['file']) }}"
                                                    type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        @else
                                            <div
                                                class="flex items-center space-x-2 p-2 bg-slate-100 dark:bg-slate-600 rounded-lg">
                                                <svg class="h-5 w-5 text-slate-500" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                                    </path>
                                                </svg>
                                                <div class="flex-1 min-w-0">
                                                    <p
                                                        class="text-sm font-medium text-slate-900 dark:text-white truncate">
                                                        {{ basename($message['file']) }}
                                                    </p>
                                                </div>
                                                <a href="{{ asset('storage/' . $message['file']) }}" download
                                                    class="text-indigo-500 hover:text-indigo-600">
                                                    <svg class="h-4 w-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                                        </path>
                                                    </svg>
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                @endif
                            </div>

                            <!-- Timestamp -->
                            <div class="mt-1 {{ $message['sender'] === 'admin' ? 'text-right' : 'text-left' }}">
                                <span class="text-xs text-slate-500 dark:text-slate-400">
                                    {{ \Carbon\Carbon::parse($message['created_at'])->format('M j, g:i A') }}
                                </span>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="flex items-center justify-center h-full">
                        <div class="text-center">
                            <svg class="mx-auto h-12 w-12 text-slate-400" xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24" fill="currentColor" class="size-6">
                                <path
                                    d="M4.913 2.658c2.075-.27 4.19-.408 6.337-.408 2.147 0 4.262.139 6.337.408 1.922.25 3.291 1.861 3.405 3.727a4.403 4.403 0 0 0-1.032-.211 50.89 50.89 0 0 0-8.42 0c-2.358.196-4.04 2.19-4.04 4.434v4.286a4.47 4.47 0 0 0 2.433 3.984L7.28 21.53A.75.75 0 0 1 6 21v-4.03a48.527 48.527 0 0 1-1.087-.128C2.905 16.58 1.5 14.833 1.5 12.862V6.638c0-1.97 1.405-3.718 3.413-3.979Z" />
                                <path
                                    d="M15.75 7.5c-1.376 0-2.739.057-4.086.169C10.124 7.797 9 9.103 9 10.609v4.285c0 1.507 1.128 2.814 2.67 2.94 1.243.102 2.5.157 3.768.165l2.782 2.781a.75.75 0 0 0 1.28-.53v-2.39l.33-.026c1.542-.125 2.67-1.433 2.67-2.94v-4.286c0-1.505-1.125-2.811-2.664-2.94A49.392 49.392 0 0 0 15.75 7.5Z" />
                            </svg>

                            <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No messages yet</h3>
                            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">Start the conversation by
                                sending a message.</p>
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Message Input Area -->
            <div class="bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 p-4">
                <!-- File Upload Preview -->
                @if ($uploadedFile)
                    <div class="mb-4 p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <svg class="h-5 w-5 text-slate-500" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13">
                                    </path>
                                </svg>
                                <span
                                    class="text-sm text-slate-700 dark:text-slate-300">{{ $uploadedFile->getClientOriginalName() }}</span>
                            </div>
                            <button wire:click="removeFile" class="text-red-500 hover:text-red-600">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                @endif

                <!-- File Upload Area (when toggled) -->
                @if ($showFileUpload && !$uploadedFile)
                    <div class="mb-4 p-4 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg">
                        <div class="text-center">
                            <svg class="mx-auto h-8 w-8 text-slate-400" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12">
                                </path>
                            </svg>
                            <div class="mt-2">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="text-sm text-indigo-600 hover:text-indigo-500">Upload a file</span>
                                    <input id="file-upload" wire:model="uploadedFile" type="file" class="sr-only"
                                        accept="image/*,video/*,.pdf,.doc,.docx,.txt">
                                </label>
                                <p class="text-xs text-slate-500 mt-1">Images, videos, or documents</p>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Message Input -->
                <form wire:submit="sendMessage" class="flex items-end space-x-2">
                    <div class="flex-1">
                        <flux:textarea wire:model.live.debounce.500ms="newMessage" placeholder="Type your message..."
                            rows="1" class="resize-none" wire:keydown.enter.prevent="sendMessage" />
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center space-x-2">
                        <!-- File Upload Toggle -->
                        <flux:button type="button" wire:click="toggleFileUpload" variant="ghost" size="sm"
                            class="p-2 {{ $showFileUpload ? 'text-indigo-600' : 'text-slate-400' }}">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13">
                                </path>
                            </svg>
                        </flux:button>

                        <!-- Send Button -->
                        <flux:button type="submit" variant="primary" size="sm"
                            :disabled="empty(trim($newMessage)) && !$uploadedFile">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </flux:button>
                    </div>
                </form>
            </div>
        @else
            <!-- No Chat Selected -->
            <div class="flex-1 flex flex-col bg-white dark:bg-slate-800">
                <!-- Header with toggle button for mobile -->
                <div class="lg:hidden bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-4">
                    <button wire:click="toggleSidebar"
                        class="p-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>

                <!-- Content -->
                <div class="flex-1 flex items-center justify-center">
                    <div class="text-center">
                        <svg class="mx-auto h-16 w-16 text-slate-400" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z">
                            </path>
                        </svg>
                        <h3 class="mt-4 text-lg font-medium text-slate-900 dark:text-white">Select a chat to start
                            messaging</h3>
                        <p class="mt-2 text-sm text-slate-500 dark:text-slate-400">Choose a dentist from the sidebar to
                            begin the conversation.</p>
                        <div class="mt-4 lg:hidden">
                            <flux:button wire:click="toggleSidebar" variant="primary" size="sm">
                                View Chats
                            </flux:button>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
    <!-- New Chat Modal -->
    <flux:modal wire:model="showNewChatModal" class="max-w-md w-full">
        <div class="p-6">
            <div class="flex items-center space-x-3 mb-6">
                <div class="h-10 w-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                    <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-slate-900 dark:text-white">Start New Chat</h3>
                    <p class="text-sm text-slate-500 dark:text-slate-400">Select a dentist to start a conversation</p>
                </div>
            </div>

            <div class="space-y-2 max-h-96 overflow-y-auto">
                @forelse($dentistsWithoutChats as $dentist)
                    <div wire:click="selectDentist({{ $dentist->id }})"
                        class="p-3 border border-slate-200 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer transition-colors">
                        <div class="flex items-center space-x-3">
                            <!-- Avatar -->
                            <div
                                class="h-8 w-8 bg-indigo-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                                {{ $dentist->initials() }}
                            </div>

                            <!-- Dentist Info -->
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm font-medium text-slate-900 dark:text-white truncate">
                                    {{ $dentist->name }}
                                </h4>
                                <p class="text-xs text-slate-500 dark:text-slate-400 truncate">
                                    {{ $dentist->email }}
                                </p>
                            </div>

                            <div class="text-xs text-slate-400">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                            </path>
                        </svg>
                        <h4 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">All dentists have chats
                        </h4>
                        <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">Every active dentist already has a
                            conversation started.</p>
                    </div>
                @endforelse
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <flux:button wire:click="closeNewChatModal" variant="ghost">
                    Cancel
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>


<!-- Auto-scroll to bottom script -->
<script>
    document.addEventListener('livewire:init', () => {
        Livewire.on('message-sent', () => {
            const container = document.getElementById('messages-container');
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        });
    });

    // Auto-scroll when messages load
    document.addEventListener('DOMContentLoaded', function() {
        const container = document.getElementById('messages-container');
        if (container) {
            container.scrollTop = container.scrollHeight;
        }
    });

    // Handle window resize to show sidebar on desktop
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) { // lg breakpoint
            @this.set('showSidebar', true);
        }
    });
</script>
