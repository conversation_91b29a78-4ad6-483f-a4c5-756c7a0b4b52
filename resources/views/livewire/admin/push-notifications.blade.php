<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-slate-900 dark:text-white">Push Notifications</h1>
            <p class="mt-1 text-sm text-slate-600 dark:text-slate-400">
                Send push notifications to dentists
            </p>
        </div>
        
        <!-- Stats -->
        <div class="flex items-center space-x-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ $totalDentists }}</div>
                <div class="text-xs text-slate-500 dark:text-slate-400">Total Dentists</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $activeDentists }}</div>
                <div class="text-xs text-slate-500 dark:text-slate-400">Active</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $selectedCount }}</div>
                <div class="text-xs text-slate-500 dark:text-slate-400">Selected</div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Left Column: Notification Form -->
        <div class="space-y-6">
            <!-- Notification Form -->
            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="h-10 w-10 bg-blue-500 rounded-lg flex items-center justify-center">
                        <flux:icon name="bell" class="h-5 w-5 text-white" />
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-900 dark:text-white">Create Notification</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">Compose your push notification</p>
                    </div>
                </div>

                <form wire:submit="sendNotification" class="space-y-4">
                    <!-- Title Field -->
                    <div>
                        <flux:input 
                            wire:model.blur="title" 
                            label="Notification Title" 
                            placeholder="Enter notification title..."
                            maxlength="255"
                            required
                        />
                        <div class="mt-1 text-xs {{ strlen($title) > 200 ? 'text-amber-600 dark:text-amber-400' : 'text-slate-500 dark:text-slate-400' }}">
                            {{ strlen($title) }}/255 characters
                        </div>
                    </div>

                    <!-- Message Field -->
                    <div>
                        <flux:textarea 
                            wire:model.blur="message" 
                            label="Notification Message" 
                            placeholder="Enter your notification message..."
                            rows="4"
                            maxlength="1000"
                            required
                        />
                        <div class="mt-1 text-xs {{ strlen($message) > 800 ? 'text-amber-600 dark:text-amber-400' : 'text-slate-500 dark:text-slate-400' }}">
                            {{ strlen($message) }}/1000 characters
                        </div>
                    </div>

                    <!-- Preview Toggle -->
                    @if($title || $message)
                        <div class="pt-2">
                            <flux:button 
                                wire:click="togglePreview" 
                                variant="outline" 
                                size="sm"
                                icon="{{ $showPreview ? 'eye-slash' : 'eye' }}"
                            >
                                {{ $showPreview ? 'Hide Preview' : 'Show Preview' }}
                            </flux:button>
                        </div>
                    @endif

                    <!-- Preview -->
                    @if($showPreview && ($title || $message))
                        <div class="bg-slate-50 dark:bg-slate-700 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                            <h4 class="text-sm font-medium text-slate-900 dark:text-white mb-2">Preview</h4>
                            <div class="bg-white dark:bg-slate-800 rounded-lg p-3 border border-slate-200 dark:border-slate-600 shadow-sm">
                                @if($title)
                                    <div class="font-semibold text-slate-900 dark:text-white text-sm">{{ $title }}</div>
                                @endif
                                @if($message)
                                    <div class="text-slate-600 dark:text-slate-400 text-sm mt-1">{{ $message }}</div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Selected Recipients Summary -->
                    @if(count($selectedDentists) > 0)
                        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">
                                        {{ count($selectedDentists) }} Dentist{{ count($selectedDentists) > 1 ? 's' : '' }} Selected
                                    </h4>
                                    <p class="text-xs text-blue-700 dark:text-blue-300 mt-1">
                                        Notification will be sent to selected dentists
                                    </p>
                                </div>
                                <flux:button 
                                    wire:click="clearSelection" 
                                    variant="outline" 
                                    size="sm"
                                    class="text-blue-600 border-blue-300 hover:bg-blue-50"
                                >
                                    Clear All
                                </flux:button>
                            </div>
                        </div>
                    @endif

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700">
                        <div class="text-sm text-slate-600 dark:text-slate-400">
                            @if(count($selectedDentists) > 0)
                                Ready to send to {{ count($selectedDentists) }} dentist{{ count($selectedDentists) > 1 ? 's' : '' }}
                            @else
                                Select dentists to send notification
                            @endif
                        </div>
                        
                        <flux:button
                            type="submit"
                            variant="primary"
                            icon="paper-airplane"
                            :disabled="!$title || !$message || count($selectedDentists) === 0"
                            wire:loading.attr="disabled"
                            wire:target="sendNotification"
                        >
                            <span wire:loading.remove wire:target="sendNotification">Send Notification</span>
                            <span wire:loading wire:target="sendNotification" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Sending...
                            </span>
                        </flux:button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Right Column: Dentist Selection -->
        <div class="space-y-6">
            <!-- Search and Filters -->
            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-4">
                <div class="space-y-4">
                    <!-- Search Input -->
                    <div>
                        <flux:input 
                            wire:model.live.debounce.300ms="search" 
                            placeholder="Search dentists by name, email, or phone..." 
                            icon="magnifying-glass"
                        />
                    </div>

                    <!-- Status Filter -->
                    <div class="flex items-center space-x-4">
                        <flux:select wire:model.live="statusFilter" placeholder="All Statuses" class="flex-1">
                            <option value="">All Statuses</option>
                            <option value="active">Active Only</option>
                            <option value="inactive">Inactive Only</option>
                        </flux:select>

                        @if($search || $statusFilter)
                            <flux:button 
                                wire:click="$set('search', ''); $set('statusFilter', '')" 
                                variant="outline" 
                                size="sm"
                                icon="x-mark"
                            >
                                Clear
                            </flux:button>
                        @endif
                    </div>

                    <!-- Bulk Actions -->
                    <div class="flex items-center justify-between pt-2 border-t border-slate-200 dark:border-slate-700">
                        <div class="flex items-center space-x-2">
                            <flux:checkbox 
                                wire:model.live="selectAll" 
                                label="Select All Visible"
                            />
                        </div>
                        
                        @if(count($selectedDentists) > 0)
                            <flux:button 
                                wire:click="selectAllVisible" 
                                variant="outline" 
                                size="sm"
                            >
                                Select All ({{ $dentists->total() }})
                            </flux:button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Dentist List -->
            <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
                <div class="p-4 border-b border-slate-200 dark:border-slate-700">
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white">
                        Select Dentists
                        @if($dentists->total() > 0)
                            <span class="text-sm font-normal text-slate-500 dark:text-slate-400">
                                ({{ $dentists->total() }} total)
                            </span>
                        @endif
                    </h3>
                </div>

                <div class="max-h-96 overflow-y-auto">
                    @forelse($dentists as $dentist)
                        <div class="p-4 border-b border-slate-100 dark:border-slate-700 last:border-b-0 hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors">
                            <div class="flex items-center space-x-3">
                                <flux:checkbox 
                                    wire:click="toggleDentist({{ $dentist->id }})"
                                    :checked="in_array($dentist->id, $selectedDentists)"
                                />
                                
                                <!-- Avatar -->
                                <div class="h-10 w-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                    {{ $dentist->initials() }}
                                </div>
                                
                                <!-- Dentist Info -->
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="text-sm font-medium text-slate-900 dark:text-white truncate">
                                            {{ $dentist->name }}
                                        </h4>
                                        <flux:badge 
                                            :variant="$dentist->is_active ? 'success' : 'danger'" 
                                            size="sm"
                                        >
                                            {{ $dentist->status_text }}
                                        </flux:badge>
                                    </div>
                                    <p class="text-xs text-slate-500 dark:text-slate-400 truncate">
                                        {{ $dentist->email }}
                                    </p>
                                    @if($dentist->phone)
                                        <p class="text-xs text-slate-500 dark:text-slate-400">
                                            {{ $dentist->phone }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="p-8 text-center">
                            <div class="mx-auto h-12 w-12 text-slate-400 dark:text-slate-500 mb-4">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-sm font-medium text-slate-900 dark:text-white">No dentists found</h3>
                            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                                @if($search || $statusFilter)
                                    No dentists match your current filters. Try adjusting your search criteria.
                                @else
                                    No dentists are available in the system.
                                @endif
                            </p>
                        </div>
                    @endforelse
                </div>

                <!-- Pagination -->
                @if($dentists->hasPages())
                    <div class="p-4 border-t border-slate-200 dark:border-slate-700">
                        {{ $dentists->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
