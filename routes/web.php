<?php

use App\Livewire\Admin\CasesManagement;
use App\Livewire\Admin\Dashboard;
use App\Livewire\Admin\DentistChat;
use App\Livewire\Admin\DentistsManagement;
use App\Livewire\Admin\PushNotifications;
use App\Livewire\Auth\Login;
use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use Illuminate\Support\Facades\Route;

Route::get('/', Login::class)->name('home');

Route::get('dashboard', Dashboard::class)
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');
});

// Admin Routes
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('dentists', DentistsManagement::class)->name('dentists');
    Route::get('cases', CasesManagement::class)->name('cases');
    Route::get('chat/{dentistId?}', DentistChat::class)->name('chat');
    Route::get('push-notifications', PushNotifications::class)->name('push-notifications');
});

require __DIR__.'/auth.php';
